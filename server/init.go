package server

import (
	basicPriceDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/basic_price"
	blockDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/block"
	customerDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/customer"
	dailyreportadditionDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/daily_report_addition"
	departmentDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/department"
	departmentpicDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/department_pic"
	distantfeeDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/distant_fee"
	healthDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/health"
	incometaxDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/income_tax"
	optionDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/option"
	qualificationDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/qualification"
	signatureDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/signature"
	sitereportDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report"
	sitereportdailyreportadditionDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_daily_report_addition"
	sitereportoptionDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_option"
	sitereportstatutoryDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_statutory"
	sitereportworkerDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_worker"
	sitereportworkerqualificationDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_worker_qualification"
	statutoryDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/statutory"
	userDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/user"
	userRoleDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/user_role"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/config"
	addfeeusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/add_fee"
	basicpriceusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/basic_price"
	blockusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/block"
	customerusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/customer"
	distantfeeusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/distant_fee"
	healthusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/health"
	optionusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/option"
	qualificationusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/qualification"
	signatureusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/signature"
	sitereportusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/site_report"
	userusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/user"
)

var (
	// usecases
	HealthUseCase        *healthusecase.HealthUseCase
	UserUseCase          *userusecase.UserUseCase
	SiteReportUseCase    *sitereportusecase.SiteReportUseCase
	CustomerUseCase      *customerusecase.CustomerUseCase
	BasicPriceUseCase    *basicpriceusecase.BasicPriceUseCase
	BlockUseCase         *blockusecase.BlockUseCase
	OptionUseCase        *optionusecase.OptionUseCase
	QualificationUseCase *qualificationusecase.QualificationUseCase
	SignatureUseCase     *signatureusecase.SignatureUseCase
	DistantFeeUseCase    *distantfeeusecase.DistantFeeUseCase
	AddFeeUseCase        *addfeeusecase.AddFeeUseCase

	// domains
	healthDomain                        healthDmn.HealthDomain
	userDomain                          userDmn.UserDomain
	userRoleDomain                      userRoleDmn.UserRoleDomain
	siteReportDomain                    sitereportDmn.SiteReportDomain
	siteReportDailyReportAdditionDomain sitereportdailyreportadditionDmn.SiteReportDailyReportAdditionDomain
	siteReportOptionDomain              sitereportoptionDmn.SiteReportOptionDomain
	siteReportStatutoryDomain           sitereportstatutoryDmn.SiteReportStatutoryDomain
	customerDomain                      customerDmn.CustomerDomain
	departmentDomain                    departmentDmn.DepartmentDomain
	departmentPicDomain                 departmentpicDmn.DepartmentPicDomain
	distantFeeDomain                    distantfeeDmn.DistantFeeDomain
	incomeTaxDomain                     incometaxDmn.IncomeTaxDomain
	basicPriceDomain                    basicPriceDmn.BasicPriceDomain
	blockDomain                         blockDmn.BlockDomain
	optionDomain                        optionDmn.OptionDomain
	qualificationDomain                 qualificationDmn.QualificationDomain
	signatureDomain                     signatureDmn.SignatureDomain
	statutoryDomain                     statutoryDmn.StatutoryDomain
	dailyReportAdditionDomain           dailyreportadditionDmn.DailyReportAdditionDomain
	siteReportWorkerDomain              sitereportworkerDmn.SiteReportWorkerDomain
	siteReportWorkerQualificationDomain sitereportworkerqualificationDmn.SiteReportWorkerQualificationDomain
)

func Init(mode ...string) error {
	config.InitDatabase()
	config.InitGoAdmin()
	config.AwsInit()
	InitAccountingSystem()

	return nil
}

func InitAccountingSystem() {
	healthDomain = healthDmn.InitHealthDomain(healthDmn.HealthResource{})
	userDomain = userDmn.InitUserDomain(userDmn.UserResource{})
	userRoleDomain = userRoleDmn.InitUserRoleDomain(userRoleDmn.UserRoleResource{})
	siteReportDomain = sitereportDmn.InitSiteReportDomain(sitereportDmn.SiteReportResource{})
	siteReportDailyReportAdditionDomain = sitereportdailyreportadditionDmn.InitSiteReportDailyReportAdditionDomain(sitereportdailyreportadditionDmn.SiteReportDailyReportAdditionResource{})
	siteReportOptionDomain = sitereportoptionDmn.InitSiteReportOptionDomain(sitereportoptionDmn.SiteReportOptionResource{})
	siteReportStatutoryDomain = sitereportstatutoryDmn.InitSiteReportStatutoryDomain(sitereportstatutoryDmn.SiteReportStatutoryResource{})
	customerDomain = customerDmn.InitCustomerDomain(customerDmn.CustomerResource{})
	departmentDomain = departmentDmn.InitDepartmentDomain(departmentDmn.DepartmentResource{})
	departmentPicDomain = departmentpicDmn.InitDepartmentPicDomain(departmentpicDmn.DepartmentPicResource{})
	basicPriceDomain = basicPriceDmn.InitBasicPriceDomain(basicPriceDmn.BasicPriceResource{})
	blockDomain = blockDmn.InitBlockDomain(blockDmn.BlockResource{})
	optionDomain = optionDmn.InitOptionDomain(optionDmn.OptionResource{})
	qualificationDomain = qualificationDmn.InitQualificationDomain(qualificationDmn.QualificationResource{})
	signatureDomain = signatureDmn.InitSignatureDomain(signatureDmn.SignatureResource{})
	distantFeeDomain = distantfeeDmn.InitDistantFeeDomain(distantfeeDmn.DistantFeeResource{})
	incomeTaxDomain = incometaxDmn.InitIncomeTaxDomain(incometaxDmn.IncomeTaxResource{})
	statutoryDomain = statutoryDmn.InitStatutoryDomain(statutoryDmn.StatutoryResource{})
	dailyReportAdditionDomain = dailyreportadditionDmn.InitDailyReportAdditionDomain(dailyreportadditionDmn.DailyReportAdditionResource{})
	siteReportWorkerDomain = sitereportworkerDmn.InitSiteReportWorkerDomain(sitereportworkerDmn.SiteReportWorkerResource{})
	siteReportWorkerQualificationDomain = sitereportworkerqualificationDmn.InitSiteReportWorkerQualificationDomain(sitereportworkerqualificationDmn.SiteReportWorkerQualificationResource{})

	healthDomains := healthusecase.Domains{
		HealthDomain: &healthDomain,
	}

	userDomains := userusecase.Domains{
		UserDomain:     &userDomain,
		UserRoleDomain: &userRoleDomain,
	}

	siteReportDomains := sitereportusecase.Domains{
		SiteReportDomain:                    &siteReportDomain,
		SiteReportDailyReportAdditionDomain: &siteReportDailyReportAdditionDomain,
		SiteReportOptionDomain:              &siteReportOptionDomain,
		SiteReportStatutoryDomain:           &siteReportStatutoryDomain,
		BasicPriceDomain:                    &basicPriceDomain,
		BlockDomain:                         &blockDomain,
		StatutoryDomain:                     &statutoryDomain,
		DailyReportAdditionDomain:           &dailyReportAdditionDomain,
		DepartmentPicDomain:                 &departmentPicDomain,
		DistantFeeDomain:                    &distantFeeDomain,
		IncomeTaxDomain:                     &incomeTaxDomain,
		OptionDomain:                        &optionDomain,
		QualificationDomain:                 &qualificationDomain,
		SiteReportWorkerDomain:              &siteReportWorkerDomain,
		SiteReportWorkerQualificationDomain: &siteReportWorkerQualificationDomain,
		UserDomain:                          &userDomain,
	}

	customerDomains := customerusecase.Domains{
		CustomerDomain:      &customerDomain,
		DepartmentDomain:    &departmentDomain,
		DepartmentPicDomain: &departmentPicDomain,
	}

	basicPriceDomains := basicpriceusecase.Domains{
		BasicPriceDomain: &basicPriceDomain,
	}

	blockDomains := blockusecase.Domains{
		BlockDomain: &blockDomain,
	}

	optionDomains := optionusecase.Domains{
		OptionDomain: &optionDomain,
	}

	qualificationDomains := qualificationusecase.Domains{
		QualificationDomain: &qualificationDomain,
	}

	signatureDomains := signatureusecase.Domains{
		SignatureDomain: &signatureDomain,
	}

	distantFeeDomains := distantfeeusecase.Domains{
		DistantFeeDomain: &distantFeeDomain,
	}

	addFeeDomains := addfeeusecase.Domains{
		DailyReportAdditionDomain: &dailyReportAdditionDomain,
	}

	HealthUseCase = healthusecase.InitHealthUseCase(healthDomains)
	UserUseCase = userusecase.InitUserUseCase(userDomains)
	SiteReportUseCase = sitereportusecase.InitSiteReportUseCase(siteReportDomains)
	CustomerUseCase = customerusecase.InitCustomerUseCase(customerDomains)
	BasicPriceUseCase = basicpriceusecase.InitBasicPriceUseCase(basicPriceDomains)
	BlockUseCase = blockusecase.InitBlockUseCase(blockDomains)
	OptionUseCase = optionusecase.InitOptionUseCase(optionDomains)
	QualificationUseCase = qualificationusecase.InitQualificationUseCase(qualificationDomains)
	SignatureUseCase = signatureusecase.InitSignatureUseCase(signatureDomains)
	DistantFeeUseCase = distantfeeusecase.InitDistantFeeUseCase(distantFeeDomains)
	AddFeeUseCase = addfeeusecase.InitAddFeeUseCase(addFeeDomains)
}
