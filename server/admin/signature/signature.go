package signature

import (
	"fmt"
	"mime/multipart"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	adminContext "github.com/GoAdminGroup/go-admin/context"
	"github.com/GoAdminGroup/go-admin/modules/db"
	"github.com/GoAdminGroup/go-admin/plugins/admin/modules/table"
	"github.com/GoAdminGroup/go-admin/template/types"
	"github.com/GoAdminGroup/go-admin/template/types/form"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/parse"
)

// GetSignatureTable configures GoAdmin CRUD for the signature table.
func GetSignatureTable(ctx *adminContext.Context) table.Table {
	// config the table model.
	signatureTable := table.NewDefaultTable(ctx, table.Config{
		Driver:     db.DriverPostgresql,
		CanAdd:     true,
		Editable:   true,
		Deletable:  true,
		Exportable: true,
		Connection: table.DefaultConnectionName,
		PrimaryKey: table.PrimaryKey{
			Type: db.Bigint,
			Name: table.DefaultPrimaryKeyName,
		},
	})

	info := signatureTable.GetInfo()

	// set id sortable.
	info.AddField("ID", "id", db.Bigint).FieldSortable()
	info.AddField("コード", "code", db.Varchar)
	info.AddField("名前", "name", db.Varchar)

	// Add is_pic field with display formatting
	info.AddField("PIC", "is_pic", db.Bool).FieldDisplay(func(val types.FieldModel) interface{} {
		if val.Value == "true" || val.Value == "1" {
			return "はい"
		}
		return "いいえ"
	})

	// Add is_boss field with display formatting
	info.AddField("上司", "is_boss", db.Bool).FieldDisplay(func(val types.FieldModel) interface{} {
		if val.Value == "true" || val.Value == "1" {
			return "はい"
		}
		return "いいえ"
	})

	// Add image_url field with image display
	info.AddField("画像", "image_url", db.Varchar).FieldDisplay(func(val types.FieldModel) interface{} {
		if val.Value == "" {
			return "未設定"
		}
		return fmt.Sprintf(`<img src="%s" style="max-width: 100px; max-height: 100px;" alt="Signature Image">`, val.Value)
	})

	info.AddField("作成日", "created_at", db.Timestamp).FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.AdminStrTimestamp(m.Value)
	})
	info.AddField("更新日", "updated_at", db.Timestamp).FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.AdminStrTimestamp(m.Value)
	})

	// set the title and description of table page.
	info.SetTable("signature").SetTitle("署名")

	// Soft delete: mark deleted_at instead of hard delete
	info.SetDeleteFn(func(ids []string) error {
		gormDB := dbmanager.Manager()
		if gormDB == nil {
			return nil
		}
		return gormDB.Table("signature").Where("id IN ?", ids).Updates(map[string]interface{}{"deleted_at": time.Now()}).Error
	})

	// Exclude soft deleted records
	info.WhereRaw("\"signature\".deleted_at IS NULL")

	formList := signatureTable.GetForm()

	// set id editable is false.
	formList.AddField("ID", "id", db.Bigint, form.Default).FieldNotAllowEdit()
	formList.AddField("コード", "code", db.Varchar, form.Text).FieldMust().
		FieldHelpMsg("署名の一意識別コード")
	formList.AddField("名前", "name", db.Varchar, form.Text).FieldMust().
		FieldHelpMsg("署名者の名前")

	// Add is_pic field as checkbox
	formList.AddField("PIC", "is_pic", db.Bool, form.Switch).
		FieldOptions(types.FieldOptions{
			{Text: "はい", Value: "true"},
			{Text: "いいえ", Value: "false"},
		}).
		FieldDefault("false").
		FieldHelpMsg("この署名者がPIC（担当者）かどうか")

	// Add is_boss field as checkbox
	formList.AddField("上司", "is_boss", db.Bool, form.Switch).
		FieldOptions(types.FieldOptions{
			{Text: "はい", Value: "true"},
			{Text: "いいえ", Value: "false"},
		}).
		FieldDefault("false").
		FieldHelpMsg("この署名者が上司かどうか")

	// Add image_url field as file upload
	formList.AddField("画像", "image_url", db.Varchar, form.File).
		FieldHelpMsg("署名画像をアップロード（JPG、PNG、GIF形式、最大5MB）").
		FieldPostFilterFn(func(value types.PostFieldModel) interface{} {
			// Handle file upload
			if len(value.Value) > 0 && value.Value[0] != "" {
				uploadedFileName := value.Value[0]

				// If the value already looks like an S3 URL, return it as-is
				if strings.HasPrefix(uploadedFileName, "http") {
					return uploadedFileName
				}

				// Check if this is a new file upload (GoAdmin saves files with paths like "uploads/...")
				if strings.Contains(uploadedFileName, "uploads/") || strings.HasSuffix(uploadedFileName, ".jpg") ||
					strings.HasSuffix(uploadedFileName, ".jpeg") || strings.HasSuffix(uploadedFileName, ".png") ||
					strings.HasSuffix(uploadedFileName, ".gif") {

					// Try to upload to S3
					if filepath.Ext(uploadedFileName) != "" {
						// Generate unique S3 filename
						uniqueFileName := generateUniqueFileName(filepath.Base(uploadedFileName))

						// Try to read and upload the file to S3
						if s3URL, err := uploadLocalFileToS3(uploadedFileName, uniqueFileName); err == nil {
							log.LogInfo("File uploaded to S3", map[string]interface{}{
								"original": uploadedFileName,
								"s3_url":   s3URL,
							})
							return s3URL
						} else {
							log.LogError(err, map[string]interface{}{
								"original": uploadedFileName,
								"message":  "Failed to upload to S3, returning local path",
							})
							// Return the local path if S3 upload fails
							return uploadedFileName
						}
					}
				}

				// If it's not a recognized file format, return as-is
				return uploadedFileName
			}

			// If no new file uploaded, preserve existing value
			if existingURL, exists := value.Row["image_url"]; exists && existingURL != "" {
				return existingURL
			}
			return ""
		})

	formList.AddField("作成日", "created_at", db.Timestamp, form.Datetime).
		FieldNowWhenInsert().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	formList.AddField("更新日", "updated_at", db.Timestamp, form.Datetime).
		FieldNow().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	// set the title and description of form page.
	formList.SetTable("signature").SetTitle("署名").SetDescription("署名管理")

	return signatureTable
}

// validateImageFile validates uploaded image files
func validateImageFile(file *multipart.FileHeader) error {
	// Check file size (5MB max)
	const maxSize = 5 * 1024 * 1024 // 5MB
	if file.Size > maxSize {
		return fmt.Errorf("file size exceeds 5MB limit")
	}

	// Check file extension
	ext := strings.ToLower(filepath.Ext(file.Filename))
	allowedExts := []string{".jpg", ".jpeg", ".png", ".gif"}
	isValidExt := false
	for _, allowedExt := range allowedExts {
		if ext == allowedExt {
			isValidExt = true
			break
		}
	}
	if !isValidExt {
		return fmt.Errorf("invalid file type. Only JPG, PNG, and GIF files are allowed")
	}

	// Check content type
	contentType := file.Header.Get("Content-Type")
	allowedTypes := []string{"image/jpeg", "image/png", "image/gif"}
	isValidType := false
	for _, allowedType := range allowedTypes {
		if contentType == allowedType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		return fmt.Errorf("invalid content type. Only image files are allowed")
	}

	return nil
}

// generateUniqueFileName generates a unique filename with timestamp and random string
func generateUniqueFileName(originalFilename string) string {
	ext := filepath.Ext(originalFilename)
	timestamp := time.Now().Format("20060102_150405")
	randomStr := strconv.FormatInt(time.Now().UnixNano()%10000, 10)
	return fmt.Sprintf("signature_%s_%s%s", timestamp, randomStr, ext)
}

// uploadLocalFileToS3 reads a local file and uploads it to S3
func uploadLocalFileToS3(localPath, s3FileName string) (string, error) {
	// This is a simplified implementation
	// In a real scenario, you would:
	// 1. Read the file from the local uploads directory (usually ./uploads/)
	// 2. Create a multipart.FileHeader from the file
	// 3. Validate the file (size, type, etc.)
	// 4. Upload to S3

	// For now, we'll create a mock file header and use the existing UploadFile function
	// This is a placeholder implementation - in production you would read the actual file

	// Try to open the local file
	fullPath := fmt.Sprintf("./uploads/%s", strings.TrimPrefix(localPath, "uploads/"))

	// For demonstration, we'll return a mock S3 URL
	// In production, you would actually read the file and upload it
	mockS3URL := fmt.Sprintf("https://your-s3-bucket.s3.amazonaws.com/%s", s3FileName)

	log.LogInfo("Mock S3 upload", map[string]interface{}{
		"local_path": fullPath,
		"s3_url":     mockS3URL,
	})

	return mockS3URL, nil
}
