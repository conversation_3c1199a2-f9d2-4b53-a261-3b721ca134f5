package signature

import (
	"context"
	"fmt"
	"mime/multipart"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	adminContext "github.com/GoAdminGroup/go-admin/context"
	"github.com/GoAdminGroup/go-admin/modules/db"
	"github.com/GoAdminGroup/go-admin/plugins/admin/modules/table"
	"github.com/GoAdminGroup/go-admin/template/types"
	"github.com/GoAdminGroup/go-admin/template/types/form"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/s3"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	awsCfg "github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/aws"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/parse"
)

// GetSignatureTable configures GoAdmin CRUD for the signature table.
func GetSignatureTable(ctx *adminContext.Context) table.Table {
	// config the table model.
	signatureTable := table.NewDefaultTable(ctx, table.Config{
		Driver:     db.DriverPostgresql,
		CanAdd:     true,
		Editable:   true,
		Deletable:  true,
		Exportable: true,
		Connection: table.DefaultConnectionName,
		PrimaryKey: table.PrimaryKey{
			Type: db.Bigint,
			Name: table.DefaultPrimaryKeyName,
		},
	})

	info := signatureTable.GetInfo()

	// set id sortable.
	info.AddField("ID", "id", db.Bigint).FieldSortable()
	info.AddField("コード", "code", db.Varchar)
	info.AddField("名前", "name", db.Varchar)

	// Add is_pic field with display formatting
	info.AddField("PIC", "is_pic", db.Bool).FieldDisplay(func(val types.FieldModel) interface{} {
		if val.Value == "true" || val.Value == "1" {
			return "はい"
		}
		return "いいえ"
	})

	// Add is_boss field with display formatting
	info.AddField("上司", "is_boss", db.Bool).FieldDisplay(func(val types.FieldModel) interface{} {
		if val.Value == "true" || val.Value == "1" {
			return "はい"
		}
		return "いいえ"
	})

	// Add image_url field with image display
	info.AddField("画像", "image_url", db.Varchar).FieldDisplay(func(val types.FieldModel) interface{} {
		if val.Value == "" {
			return "未設定"
		}
		return fmt.Sprintf(`<img src="%s" style="max-width: 100px; max-height: 100px;" alt="Signature Image">`, val.Value)
	})

	info.AddField("作成日", "created_at", db.Timestamp).FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.AdminStrTimestamp(m.Value)
	})
	info.AddField("更新日", "updated_at", db.Timestamp).FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.AdminStrTimestamp(m.Value)
	})

	// set the title and description of table page.
	info.SetTable("signature").SetTitle("署名")

	// Soft delete: mark deleted_at instead of hard delete
	info.SetDeleteFn(func(ids []string) error {
		gormDB := dbmanager.Manager()
		if gormDB == nil {
			return nil
		}
		return gormDB.Table("signature").Where("id IN ?", ids).Updates(map[string]interface{}{"deleted_at": time.Now()}).Error
	})

	// Exclude soft deleted records
	info.WhereRaw("\"signature\".deleted_at IS NULL")

	formList := signatureTable.GetForm()

	// set id editable is false.
	formList.AddField("ID", "id", db.Bigint, form.Default).FieldNotAllowEdit()
	formList.AddField("コード", "code", db.Varchar, form.Text).FieldMust().
		FieldHelpMsg("署名の一意識別コード")
	formList.AddField("名前", "name", db.Varchar, form.Text).FieldMust().
		FieldHelpMsg("署名者の名前")

	// Add is_pic field as checkbox
	formList.AddField("PIC", "is_pic", db.Bool, form.Checkbox).
		FieldOptions(types.FieldOptions{
			{Text: "はい", Value: "true"},
		}).
		FieldDefault("false").
		FieldHelpMsg("この署名者がPIC(担当者)かどうか")

	// Add is_boss field as checkbox
	formList.AddField("上司", "is_boss", db.Bool, form.Checkbox).
		FieldOptions(types.FieldOptions{
			{Text: "はい", Value: "true"},
		}).
		FieldDefault("false").
		FieldHelpMsg("この署名者が上司かどうか")

	// Add image_url field as file upload
	formList.AddField("画像", "image_url", db.Varchar, form.File).
		FieldHelpMsg("署名画像をアップロード(JPG、PNG、GIF形式、最大5MB)").
		FieldPostFilterFn(func(value types.PostFieldModel) interface{} {
			// Handle file upload
			if len(value.Value) > 0 && value.Value[0] != "" {
				uploadedFileName := value.Value[0]

				// If the value already looks like an S3 URL, return it as-is
				if strings.HasPrefix(uploadedFileName, "http") {
					return uploadedFileName
				}

				// Check if this is a new file upload (GoAdmin saves files with paths like "uploads/...")
				if strings.Contains(uploadedFileName, "uploads/") || strings.HasSuffix(uploadedFileName, ".jpg") ||
					strings.HasSuffix(uploadedFileName, ".jpeg") || strings.HasSuffix(uploadedFileName, ".png") ||
					strings.HasSuffix(uploadedFileName, ".gif") {

					// Try to upload to S3
					if filepath.Ext(uploadedFileName) != "" {
						// Generate unique S3 filename
						uniqueFileName := generateUniqueFileName(filepath.Base(uploadedFileName))

						// Try to read and upload the file to S3
						if s3URL, err := uploadLocalFileToS3(uploadedFileName, uniqueFileName); err == nil {
							log.LogInfo("File uploaded to S3", map[string]interface{}{
								"original": uploadedFileName,
								"s3_url":   s3URL,
							})
							return s3URL
						} else {
							log.LogError(err, map[string]interface{}{
								"original": uploadedFileName,
								"message":  "Failed to upload to S3, returning local path",
							})
							// Return the local path if S3 upload fails
							return uploadedFileName
						}
					}
				}

				// If it's not a recognized file format, return as-is
				return uploadedFileName
			}

			// If no new file uploaded, preserve existing value
			if existingURL, exists := value.Row["image_url"]; exists && existingURL != "" {
				return existingURL
			}
			return ""
		})

	formList.AddField("作成日", "created_at", db.Timestamp, form.Datetime).
		FieldNowWhenInsert().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	formList.AddField("更新日", "updated_at", db.Timestamp, form.Datetime).
		FieldNow().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	// set the title and description of form page.
	formList.SetTable("signature").SetTitle("署名").SetDescription("署名管理")

	return signatureTable
}

// validateImageFile validates uploaded image files with comprehensive security checks
func validateImageFile(file *multipart.FileHeader) error {
	// 1. Check file size (5MB max)
	const maxSize = 5 * 1024 * 1024 // 5MB
	if file.Size > maxSize {
		return fmt.Errorf("ファイルサイズが制限を超えています。最大5MBまでです (現在: %.2fMB)", float64(file.Size)/(1024*1024))
	}

	// 2. Check minimum file size (avoid empty files)
	const minSize = 100 // 100 bytes minimum
	if file.Size < minSize {
		return fmt.Errorf("ファイルサイズが小さすぎます。最小100バイト必要です")
	}

	// 3. Validate filename (avoid dangerous characters)
	if strings.Contains(file.Filename, "..") || strings.Contains(file.Filename, "/") || strings.Contains(file.Filename, "\\") {
		return fmt.Errorf("ファイル名に無効な文字が含まれています")
	}

	// 4. Check file extension
	ext := strings.ToLower(filepath.Ext(file.Filename))
	allowedExts := map[string]string{
		".jpg":  "image/jpeg",
		".jpeg": "image/jpeg",
		".png":  "image/png",
		".gif":  "image/gif",
	}

	expectedContentType, isValidExt := allowedExts[ext]
	if !isValidExt {
		return fmt.Errorf("サポートされていないファイル形式です。JPG、PNG、GIFファイルのみ許可されています")
	}

	// 5. Check content type matches extension
	contentType := file.Header.Get("Content-Type")
	if contentType != expectedContentType {
		return fmt.Errorf("ファイルの内容タイプが拡張子と一致しません。期待値: %s, 実際: %s", expectedContentType, contentType)
	}

	// 6. Additional security: Check for null bytes (potential security risk)
	if strings.Contains(file.Filename, "\x00") {
		return fmt.Errorf("ファイル名に無効な文字が含まれています")
	}

	log.LogInfo("File validation passed", map[string]interface{}{
		"filename":     file.Filename,
		"size":         file.Size,
		"content_type": contentType,
		"extension":    ext,
	})

	return nil
}

// validateImageContent validates the actual file content by checking magic bytes
func validateImageContent(filePath string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("failed to open file for content validation: %w", err)
	}
	defer file.Close()

	// Read the first 12 bytes to check magic numbers
	buffer := make([]byte, 12)
	n, err := file.Read(buffer)
	if err != nil {
		return fmt.Errorf("failed to read file header: %w", err)
	}
	if n < 4 {
		return fmt.Errorf("file too small to determine type")
	}

	// Check magic bytes for different image formats
	switch {
	case len(buffer) >= 3 && buffer[0] == 0xFF && buffer[1] == 0xD8 && buffer[2] == 0xFF:
		// JPEG magic bytes
		log.LogInfo("Detected JPEG file", map[string]interface{}{"file": filePath})
		return nil
	case len(buffer) >= 8 && buffer[0] == 0x89 && buffer[1] == 0x50 && buffer[2] == 0x4E && buffer[3] == 0x47 &&
		buffer[4] == 0x0D && buffer[5] == 0x0A && buffer[6] == 0x1A && buffer[7] == 0x0A:
		// PNG magic bytes
		log.LogInfo("Detected PNG file", map[string]interface{}{"file": filePath})
		return nil
	case len(buffer) >= 6 && buffer[0] == 0x47 && buffer[1] == 0x49 && buffer[2] == 0x46 &&
		buffer[3] == 0x38 && (buffer[4] == 0x37 || buffer[4] == 0x39) && buffer[5] == 0x61:
		// GIF magic bytes (GIF87a or GIF89a)
		log.LogInfo("Detected GIF file", map[string]interface{}{"file": filePath})
		return nil
	default:
		return fmt.Errorf("ファイルの内容が画像ファイルではありません。実際の画像ファイルをアップロードしてください")
	}
}

// generateUniqueFileName generates a unique filename with timestamp and random string
func generateUniqueFileName(originalFilename string) string {
	ext := filepath.Ext(originalFilename)
	timestamp := time.Now().Format("20060102_150405")
	randomStr := strconv.FormatInt(time.Now().UnixNano()%10000, 10)
	return fmt.Sprintf("signature_%s_%s%s", timestamp, randomStr, ext)
}

// uploadLocalFileToS3 reads a local file and uploads it to S3
func uploadLocalFileToS3(localPath, s3FileName string) (string, error) {
	// Build the full path to the uploaded file
	fullPath := filepath.Join("../../files", strings.TrimPrefix(localPath, "uploads/"))

	// Check if file exists
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		return "", fmt.Errorf("uploaded file not found: %s", fullPath)
	}

	// Open the file
	file, err := os.Open(fullPath)
	if err != nil {
		return "", fmt.Errorf("failed to open file %s: %w", fullPath, err)
	}
	defer file.Close()

	// Get file info for size validation
	fileInfo, err := file.Stat()
	if err != nil {
		return "", fmt.Errorf("failed to get file info: %w", err)
	}

	// Create a proper multipart.FileHeader from the file
	header := make(map[string][]string)

	// Detect content type based on file extension
	ext := strings.ToLower(filepath.Ext(fullPath))
	switch ext {
	case ".jpg", ".jpeg":
		header["Content-Type"] = []string{"image/jpeg"}
	case ".png":
		header["Content-Type"] = []string{"image/png"}
	case ".gif":
		header["Content-Type"] = []string{"image/gif"}
	default:
		return "", fmt.Errorf("unsupported file type: %s", ext)
	}

	fileHeader := &multipart.FileHeader{
		Filename: filepath.Base(fullPath),
		Size:     fileInfo.Size(),
		Header:   header,
	}

	// Validate the file metadata
	if err := validateImageFile(fileHeader); err != nil {
		return "", fmt.Errorf("file validation failed: %w", err)
	}

	// Additional validation: Check file content by reading magic bytes
	if err := validateImageContent(fullPath); err != nil {
		return "", fmt.Errorf("file content validation failed: %w", err)
	}

	// Upload directly to S3 using our file
	s3URL, err := uploadFileDirectlyToS3(fullPath, s3FileName, header["Content-Type"][0])
	if err != nil {
		return "", fmt.Errorf("S3 upload failed: %w", err)
	}

	// Clean up the local file after successful upload
	if err := os.Remove(fullPath); err != nil {
		log.LogError(err, map[string]interface{}{
			"message":    "Failed to clean up local file after S3 upload",
			"local_path": fullPath,
		})
		// Don't return error here as the S3 upload was successful
	}

	log.LogInfo("File successfully uploaded to S3", map[string]interface{}{
		"local_path": fullPath,
		"s3_url":     s3URL,
		"file_size":  fileInfo.Size(),
	})

	return s3URL, nil
}

// uploadFileDirectlyToS3 uploads a file directly to S3 without using multipart.FileHeader
func uploadFileDirectlyToS3(filePath, s3FileName, contentType string) (string, error) {
	// Get S3 configuration
	s3Manager := awsCfg.S3Manager()

	// Validate S3 configuration
	if s3Manager.Client == nil {
		return "", fmt.Errorf("S3 client not initialized. Please check AWS configuration")
	}
	if s3Manager.Bucket == "" {
		return "", fmt.Errorf("S3 bucket not configured. Please set AWS_S3_BUCKET environment variable")
	}
	if s3Manager.BaseUrl == "" {
		return "", fmt.Errorf("S3 base URL not configured. Please set AWS_BASE_URL environment variable")
	}

	// Open the file
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	log.LogInfo("Uploading file to S3", map[string]interface{}{
		"local_file":   filePath,
		"s3_key":       s3FileName,
		"bucket":       s3Manager.Bucket,
		"content_type": contentType,
	})

	// Upload to S3
	_, err = s3Manager.Client.PutObject(context.Background(), &s3.PutObjectInput{
		Bucket:      aws.String(s3Manager.Bucket),
		Key:         aws.String(s3FileName),
		Body:        file,
		ContentType: aws.String(contentType),
	})
	if err != nil {
		return "", fmt.Errorf("S3 PutObject failed: %w", err)
	}

	// Generate the S3 URL
	s3URL := fmt.Sprintf("%s/signature/%s", s3Manager.BaseUrl, s3FileName)

	log.LogInfo("File successfully uploaded to S3", map[string]interface{}{
		"s3_url": s3URL,
		"bucket": s3Manager.Bucket,
		"key":    s3FileName,
	})

	return s3URL, nil
}
