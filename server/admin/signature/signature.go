package signature

import (
	"fmt"
	"time"

	adminContext "github.com/GoAdminGroup/go-admin/context"
	"github.com/GoAdminGroup/go-admin/modules/db"
	"github.com/GoAdminGroup/go-admin/plugins/admin/modules/table"
	"github.com/GoAdminGroup/go-admin/template/types"
	"github.com/GoAdminGroup/go-admin/template/types/form"

	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/parse"
)

// GetSignatureTable configures GoAdmin CRUD for the signature table.
func GetSignatureTable(ctx *adminContext.Context) table.Table {
	// config the table model.
	signatureTable := table.NewDefaultTable(ctx, table.Config{
		Driver:     db.DriverPostgresql,
		CanAdd:     true,
		Editable:   true,
		Deletable:  true,
		Exportable: true,
		Connection: table.DefaultConnectionName,
		PrimaryKey: table.PrimaryKey{
			Type: db.Bigint,
			Name: table.DefaultPrimaryKeyName,
		},
	})

	info := signatureTable.GetInfo()

	// set id sortable.
	info.AddField("ID", "id", db.Bigint).FieldSortable()
	info.AddField("コード", "code", db.Varchar)
	info.AddField("名前", "name", db.Varchar)

	// Add is_pic field with display formatting
	info.AddField("PIC", "is_pic", db.Bool).FieldDisplay(func(val types.FieldModel) interface{} {
		if val.Value == "true" || val.Value == "1" {
			return "はい"
		}
		return "いいえ"
	})

	// Add is_boss field with display formatting
	info.AddField("上司", "is_boss", db.Bool).FieldDisplay(func(val types.FieldModel) interface{} {
		if val.Value == "true" || val.Value == "1" {
			return "はい"
		}
		return "いいえ"
	})

	// Add image_url field with image display
	info.AddField("画像", "image_url", db.Varchar).FieldDisplay(func(val types.FieldModel) interface{} {
		if val.Value == "" {
			return "未設定"
		}
		return fmt.Sprintf(`<img src="%s" style="max-width: 100px; max-height: 100px;" alt="Signature Image">`, val.Value)
	})

	info.AddField("作成日", "created_at", db.Timestamp).FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.AdminStrTimestamp(m.Value)
	})
	info.AddField("更新日", "updated_at", db.Timestamp).FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.AdminStrTimestamp(m.Value)
	})

	// set the title and description of table page.
	info.SetTable("signature").SetTitle("署名")

	// Soft delete: mark deleted_at instead of hard delete
	info.SetDeleteFn(func(ids []string) error {
		gormDB := dbmanager.Manager()
		if gormDB == nil {
			return nil
		}
		return gormDB.Table("signature").Where("id IN ?", ids).Updates(map[string]interface{}{"deleted_at": time.Now()}).Error
	})

	// Exclude soft deleted records
	info.WhereRaw("\"signature\".deleted_at IS NULL")

	formList := signatureTable.GetForm()

	// set id editable is false.
	formList.AddField("ID", "id", db.Bigint, form.Default).FieldNotAllowEdit()
	formList.AddField("コード", "code", db.Varchar, form.Text).FieldMust().
		FieldHelpMsg("署名の一意識別コード")
	formList.AddField("名前", "name", db.Varchar, form.Text).FieldMust().
		FieldHelpMsg("署名者の名前")

	// Add is_pic field as checkbox
	formList.AddField("PIC", "is_pic", db.Bool, form.Switch).
		FieldOptions(types.FieldOptions{
			{Text: "はい", Value: "true"},
			{Text: "いいえ", Value: "false"},
		}).
		FieldDefault("false").
		FieldHelpMsg("この署名者がPIC（担当者）かどうか")

	// Add is_boss field as checkbox
	formList.AddField("上司", "is_boss", db.Bool, form.Switch).
		FieldOptions(types.FieldOptions{
			{Text: "はい", Value: "true"},
			{Text: "いいえ", Value: "false"},
		}).
		FieldDefault("false").
		FieldHelpMsg("この署名者が上司かどうか")

	// Add image_url field as text input for now (file upload will be handled via custom form processing)
	formList.AddField("画像URL", "image_url", db.Varchar, form.Text).
		FieldHelpMsg("署名画像のURL（S3にアップロード後のURL）")

	formList.AddField("作成日", "created_at", db.Timestamp, form.Datetime).
		FieldNowWhenInsert().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	formList.AddField("更新日", "updated_at", db.Timestamp, form.Datetime).
		FieldNow().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	// set the title and description of form page.
	formList.SetTable("signature").SetTitle("署名").SetDescription("署名管理")

	return signatureTable
}
