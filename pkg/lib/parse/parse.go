package parse

import (
	"strconv"
	"strings"
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/utils"
)

func StrToFloat64(s string) (float64, error) {
	if s == "" {
		return 0, nil
	}

	f, err := strconv.ParseFloat(s, 64)
	if err != nil {
		return 0, err
	}

	return f, nil
}

func StrToFloat64Pointer(s string) (*float64, error) {
	if s == "" {
		return nil, nil
	}

	f, err := strconv.ParseFloat(s, 64)
	if err != nil {
		return nil, err
	}

	return &f, nil
}

func ZeroInt64Pointer(i int64) *int64 {
	if i == 0 {
		return nil
	}

	return &i
}

func Int64Pointer(i *int64) int64 {
	if i == nil {
		return 0
	}

	return *i
}

// ConvertTimeToLocalTime converts *time.Time to *utils.LocalTime
func ConvertTimeToLocalTime(t *time.Time) *utils.LocalTime {
	if t == nil {
		return nil
	}
	return &utils.LocalTime{Time: *t}
}

func StrFloatToStrInt(s string) string {
	amountSplitted := strings.Split(s, ".")
	return amountSplitted[0]
}

func AdminStrTimestamp(s string) string {
	if s == "" {
		return ""
	}
	return strings.Replace(s[:16], "T", " ", 1)
}
